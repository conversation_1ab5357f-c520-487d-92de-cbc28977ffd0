﻿# Session 32: Testing, Benchmarking, and Performance Analysis

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Master Go testing framework and best practices
- Learn benchmarking techniques and performance analysis
- Understand profiling tools: CPU, memory, execution tracing
- Apply testing strategies to automation systems
- Design comprehensive test suites for automation components

**Videos Covered**:

- 12.1 Basic Unit Testing (0:18:30)
- 12.2 Table Tests (0:16:45)
- 12.3 Sub Tests (0:14:20)
- 13.1 Basic Benchmarks (0:15:30)
- 13.2 Sub Benchmarks (0:12:15)
- 14.1 Profiling Guidelines (0:22:30)

**Key Concepts**:

- Unit testing: structure, assertions, test coverage
- Table-driven tests: testing multiple scenarios
- Benchmarking: measuring performance and memory usage
- Profiling: CPU, memory, and execution analysis
- Test organization and best practices
- Performance optimization techniques

**Hands-on Exercise 1: Unit Testing and Table-Driven Tests**:

Mastering basic unit testing and table-driven test patterns:

**Main Code (automation.go):**

```go
// Unit testing and table-driven tests for automation systems
package automation

import (
    "fmt"
    "strings"
    "time"
)

// AutomationTask represents a task in the automation system
type AutomationTask struct {
    ID          string
    Type        string
    Data        []byte
    Priority    int
    CreatedAt   time.Time
    ProcessedAt *time.Time
    Status      TaskStatus
    Metadata    map[string]string
}

type TaskStatus int

const (
    StatusPending TaskStatus = iota
    StatusProcessing
    StatusCompleted
    StatusFailed
    StatusCancelled
)

func (ts TaskStatus) String() string {
    switch ts {
    case StatusPending:
        return "pending"
    case StatusProcessing:
        return "processing"
    case StatusCompleted:
        return "completed"
    case StatusFailed:
        return "failed"
    case StatusCancelled:
        return "cancelled"
    default:
        return "unknown"
    }
}

// TaskProcessor handles task processing logic
type TaskProcessor struct {
    name            string
    maxDataSize     int
    processingDelay time.Duration
    supportedTypes  map[string]bool
}

func NewTaskProcessor(name string, maxDataSize int, delay time.Duration) *TaskProcessor {
    return &TaskProcessor{
        name:            name,
        maxDataSize:     maxDataSize,
        processingDelay: delay,
        supportedTypes: map[string]bool{
            "text":  true,
            "data":  true,
            "file":  true,
            "email": true,
            "json":  true,
        },
    }
}

// Validate checks if a task is valid for processing
func (tp *TaskProcessor) Validate(task *AutomationTask) error {
    if task == nil {
        return fmt.Errorf("task cannot be nil")
    }

    if task.ID == "" {
        return fmt.Errorf("task ID cannot be empty")
    }

    if task.Status != StatusPending {
        return fmt.Errorf("task %s is not in pending status", task.ID)
    }

    if len(task.Data) > tp.maxDataSize {
        return fmt.Errorf("task data size %d exceeds maximum %d", len(task.Data), tp.maxDataSize)
    }

    if !tp.supportedTypes[task.Type] {
        return fmt.Errorf("unsupported task type: %s", task.Type)
    }

    return nil
}

// Process handles task processing with validation
func (tp *TaskProcessor) Process(task *AutomationTask) error {
    if err := tp.Validate(task); err != nil {
        return err
    }

    // Mark as processing
    task.Status = StatusProcessing

    // Simulate processing time
    if tp.processingDelay > 0 {
        time.Sleep(tp.processingDelay)
    }

    // Process based on task type
    switch task.Type {
    case "text":
        return tp.processText(task)
    case "data":
        return tp.processData(task)
    case "file":
        return tp.processFile(task)
    case "email":
        return tp.processEmail(task)
    case "json":
        return tp.processJSON(task)
    default:
        task.Status = StatusFailed
        return fmt.Errorf("unsupported task type: %s", task.Type)
    }
}

func (tp *TaskProcessor) processText(task *AutomationTask) error {
    // Convert to uppercase and add metadata
    task.Data = []byte(strings.ToUpper(string(task.Data)))
    task.Status = StatusCompleted
    now := time.Now()
    task.ProcessedAt = &now

    if task.Metadata == nil {
        task.Metadata = make(map[string]string)
    }
    task.Metadata["processor"] = tp.name
    task.Metadata["operation"] = "uppercase"

    return nil
}

func (tp *TaskProcessor) processData(task *AutomationTask) error {
    // Reverse the data
    data := make([]byte, len(task.Data))
    copy(data, task.Data)

    for i, j := 0, len(data)-1; i < j; i, j = i+1, j-1 {
        data[i], data[j] = data[j], data[i]
    }

    task.Data = data
    task.Status = StatusCompleted
    now := time.Now()
    task.ProcessedAt = &now

    if task.Metadata == nil {
        task.Metadata = make(map[string]string)
    }
    task.Metadata["processor"] = tp.name
    task.Metadata["operation"] = "reverse"

    return nil
}

func (tp *TaskProcessor) processFile(task *AutomationTask) error {
    // Add file header
    header := []byte("PROCESSED_FILE:")
    task.Data = append(header, task.Data...)
    task.Status = StatusCompleted
    now := time.Now()
    task.ProcessedAt = &now

    if task.Metadata == nil {
        task.Metadata = make(map[string]string)
    }
    task.Metadata["processor"] = tp.name
    task.Metadata["operation"] = "add_header"

    return nil
}

func (tp *TaskProcessor) processEmail(task *AutomationTask) error {
    // Add email prefix and suffix
    prefix := []byte("EMAIL_START:")
    suffix := []byte(":EMAIL_END")
    task.Data = append(append(prefix, task.Data...), suffix...)
    task.Status = StatusCompleted
    now := time.Now()
    task.ProcessedAt = &now

    if task.Metadata == nil {
        task.Metadata = make(map[string]string)
    }
    task.Metadata["processor"] = tp.name
    task.Metadata["operation"] = "email_format"

    return nil
}

func (tp *TaskProcessor) processJSON(task *AutomationTask) error {
    // Wrap in JSON object structure
    jsonData := fmt.Sprintf(`{"data":"%s","processed_by":"%s"}`, string(task.Data), tp.name)
    task.Data = []byte(jsonData)
    task.Status = StatusCompleted
    now := time.Now()
    task.ProcessedAt = &now

    if task.Metadata == nil {
        task.Metadata = make(map[string]string)
    }
    task.Metadata["processor"] = tp.name
    task.Metadata["operation"] = "json_wrap"

    return nil
}

// GetStats returns processing statistics
func (tp *TaskProcessor) GetStats() map[string]interface{} {
    return map[string]interface{}{
        "name":            tp.name,
        "max_data_size":   tp.maxDataSize,
        "processing_delay": tp.processingDelay,
        "supported_types": len(tp.supportedTypes),
    }
}
```

**Test File (automation_test.go):**

```go
package automation

import (
    "strings"
    "testing"
    "time"
)

// PART 1: Basic Unit Tests
// Basic unit tests demonstrate fundamental testing patterns

func TestNewTaskProcessor(t *testing.T) {
    name := "TestProcessor"
    maxSize := 1024
    delay := 100 * time.Millisecond

    processor := NewTaskProcessor(name, maxSize, delay)

    if processor.name != name {
        t.Errorf("Expected name %s, got %s", name, processor.name)
    }

    if processor.maxDataSize != maxSize {
        t.Errorf("Expected maxDataSize %d, got %d", maxSize, processor.maxDataSize)
    }

    if processor.processingDelay != delay {
        t.Errorf("Expected processingDelay %v, got %v", delay, processor.processingDelay)
    }

    // Test supported types are initialized
    if !processor.supportedTypes["text"] {
        t.Error("Expected text type to be supported")
    }

    if !processor.supportedTypes["data"] {
        t.Error("Expected data type to be supported")
    }
}

func TestTaskProcessor_Validate(t *testing.T) {
    processor := NewTaskProcessor("TestProcessor", 100, 0)

    // Test nil task
    err := processor.Validate(nil)
    if err == nil {
        t.Error("Expected error for nil task, got nil")
    }

    // Test empty ID
    task := &AutomationTask{
        ID:     "",
        Type:   "text",
        Data:   []byte("test"),
        Status: StatusPending,
    }
    err = processor.Validate(task)
    if err == nil {
        t.Error("Expected error for empty ID, got nil")
    }

    // Test invalid status
    task = &AutomationTask{
        ID:     "test-1",
        Type:   "text",
        Data:   []byte("test"),
        Status: StatusCompleted,
    }
    err = processor.Validate(task)
    if err == nil {
        t.Error("Expected error for non-pending status, got nil")
    }

    // Test data size limit
    task = &AutomationTask{
        ID:     "test-1",
        Type:   "text",
        Data:   make([]byte, 200), // Exceeds maxDataSize of 100
        Status: StatusPending,
    }
    err = processor.Validate(task)
    if err == nil {
        t.Error("Expected error for oversized data, got nil")
    }

    // Test unsupported type
    task = &AutomationTask{
        ID:     "test-1",
        Type:   "unsupported",
        Data:   []byte("test"),
        Status: StatusPending,
    }
    err = processor.Validate(task)
    if err == nil {
        t.Error("Expected error for unsupported type, got nil")
    }

    // Test valid task
    task = &AutomationTask{
        ID:     "test-1",
        Type:   "text",
        Data:   []byte("test"),
        Status: StatusPending,
    }
    err = processor.Validate(task)
    if err != nil {
        t.Errorf("Expected no error for valid task, got %v", err)
    }
}

func TestTaskProcessor_ProcessText(t *testing.T) {
    processor := NewTaskProcessor("TestProcessor", 100, 0)

    task := &AutomationTask{
        ID:        "test-1",
        Type:      "text",
        Data:      []byte("hello world"),
        Priority:  5,
        CreatedAt: time.Now(),
        Status:    StatusPending,
    }

    err := processor.Process(task)
    if err != nil {
        t.Fatalf("Expected no error, got %v", err)
    }

    // Check status
    if task.Status != StatusCompleted {
        t.Errorf("Expected status %v, got %v", StatusCompleted, task.Status)
    }

    // Check data transformation
    expected := "HELLO WORLD"
    if string(task.Data) != expected {
        t.Errorf("Expected data %s, got %s", expected, string(task.Data))
    }

    // Check processed timestamp
    if task.ProcessedAt == nil {
        t.Error("Expected ProcessedAt to be set")
    }

    // Check metadata
    if task.Metadata == nil {
        t.Error("Expected metadata to be set")
    }

    if task.Metadata["processor"] != "TestProcessor" {
        t.Errorf("Expected processor metadata to be TestProcessor, got %s", task.Metadata["processor"])
    }

    if task.Metadata["operation"] != "uppercase" {
        t.Errorf("Expected operation metadata to be uppercase, got %s", task.Metadata["operation"])
    }
}

// PART 2: Table-Driven Tests
// Table-driven tests allow testing multiple scenarios efficiently

func TestTaskProcessor_ProcessAllTypes(t *testing.T) {
    processor := NewTaskProcessor("TableTestProcessor", 200, 0)

    tests := []struct {
        name         string
        taskType     string
        inputData    string
        expectedData string
        expectError  bool
        checkFunc    func(*testing.T, *AutomationTask) // Custom validation function
    }{
        {
            name:         "text processing converts to uppercase",
            taskType:     "text",
            inputData:    "hello world",
            expectedData: "HELLO WORLD",
            expectError:  false,
            checkFunc: func(t *testing.T, task *AutomationTask) {
                if task.Metadata["operation"] != "uppercase" {
                    t.Errorf("Expected operation metadata 'uppercase', got %s", task.Metadata["operation"])
                }
            },
        },
        {
            name:         "data processing reverses content",
            taskType:     "data",
            inputData:    "abc123",
            expectedData: "321cba",
            expectError:  false,
            checkFunc: func(t *testing.T, task *AutomationTask) {
                if task.Metadata["operation"] != "reverse" {
                    t.Errorf("Expected operation metadata 'reverse', got %s", task.Metadata["operation"])
                }
            },
        },
        {
            name:         "file processing adds header",
            taskType:     "file",
            inputData:    "file content",
            expectedData: "PROCESSED_FILE:file content",
            expectError:  false,
            checkFunc: func(t *testing.T, task *AutomationTask) {
                if task.Metadata["operation"] != "add_header" {
                    t.Errorf("Expected operation metadata 'add_header', got %s", task.Metadata["operation"])
                }
            },
        },
        {
            name:         "email processing adds prefix and suffix",
            taskType:     "email",
            inputData:    "<EMAIL>",
            expectedData: "EMAIL_START:<EMAIL>:EMAIL_END",
            expectError:  false,
            checkFunc: func(t *testing.T, task *AutomationTask) {
                if task.Metadata["operation"] != "email_format" {
                    t.Errorf("Expected operation metadata 'email_format', got %s", task.Metadata["operation"])
                }
            },
        },
        {
            name:         "json processing wraps in JSON object",
            taskType:     "json",
            inputData:    "test data",
            expectedData: `{"data":"test data","processed_by":"TableTestProcessor"}`,
            expectError:  false,
            checkFunc: func(t *testing.T, task *AutomationTask) {
                if task.Metadata["operation"] != "json_wrap" {
                    t.Errorf("Expected operation metadata 'json_wrap', got %s", task.Metadata["operation"])
                }
            },
        },
        {
            name:        "unsupported type returns error",
            taskType:    "unknown",
            inputData:   "test data",
            expectError: true,
        },
        {
            name:        "empty data is processed correctly",
            taskType:    "text",
            inputData:   "",
            expectedData: "",
            expectError: false,
        },
        {
            name:        "special characters in text processing",
            taskType:    "text",
            inputData:   "hello@world!123",
            expectedData: "HELLO@WORLD!123",
            expectError: false,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            task := &AutomationTask{
                ID:        "table-test",
                Type:      tt.taskType,
                Data:      []byte(tt.inputData),
                Status:    StatusPending,
                CreatedAt: time.Now(),
            }

            err := processor.Process(task)

            if tt.expectError {
                if err == nil {
                    t.Error("Expected error, got nil")
                }
                return
            }

            if err != nil {
                t.Fatalf("Expected no error, got %v", err)
            }

            // Check data transformation
            if string(task.Data) != tt.expectedData {
                t.Errorf("Expected data %s, got %s", tt.expectedData, string(task.Data))
            }

            // Check status
            if task.Status != StatusCompleted {
                t.Errorf("Expected status %v, got %v", StatusCompleted, task.Status)
            }

            // Check processed timestamp
            if task.ProcessedAt == nil {
                t.Error("Expected ProcessedAt to be set")
            }

            // Run custom validation if provided
            if tt.checkFunc != nil {
                tt.checkFunc(t, task)
            }
        })
    }
}

// PART 3: Edge Cases and Error Conditions Table Tests
func TestTaskProcessor_ErrorConditions(t *testing.T) {
    processor := NewTaskProcessor("ErrorTestProcessor", 50, 0)

    tests := []struct {
        name        string
        task        *AutomationTask
        expectError bool
        errorContains string
    }{
        {
            name:          "nil task",
            task:          nil,
            expectError:   true,
            errorContains: "cannot be nil",
        },
        {
            name: "empty task ID",
            task: &AutomationTask{
                ID:     "",
                Type:   "text",
                Data:   []byte("test"),
                Status: StatusPending,
            },
            expectError:   true,
            errorContains: "ID cannot be empty",
        },
        {
            name: "task already processing",
            task: &AutomationTask{
                ID:     "test-1",
                Type:   "text",
                Data:   []byte("test"),
                Status: StatusProcessing,
            },
            expectError:   true,
            errorContains: "not in pending status",
        },
        {
            name: "task already completed",
            task: &AutomationTask{
                ID:     "test-1",
                Type:   "text",
                Data:   []byte("test"),
                Status: StatusCompleted,
            },
            expectError:   true,
            errorContains: "not in pending status",
        },
        {
            name: "data size exceeds limit",
            task: &AutomationTask{
                ID:     "test-1",
                Type:   "text",
                Data:   make([]byte, 100), // Exceeds limit of 50
                Status: StatusPending,
            },
            expectError:   true,
            errorContains: "exceeds maximum",
        },
        {
            name: "unsupported task type",
            task: &AutomationTask{
                ID:     "test-1",
                Type:   "video",
                Data:   []byte("test"),
                Status: StatusPending,
            },
            expectError:   true,
            errorContains: "unsupported task type",
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            err := processor.Process(tt.task)

            if tt.expectError {
                if err == nil {
                    t.Error("Expected error, got nil")
                    return
                }

                if !strings.Contains(err.Error(), tt.errorContains) {
                    t.Errorf("Expected error to contain %s, got %s", tt.errorContains, err.Error())
                }
            } else {
                if err != nil {
                    t.Errorf("Expected no error, got %v", err)
                }
            }
        })
    }
}

// PART 4: Performance-focused Table Tests
func TestTaskProcessor_DataSizes(t *testing.T) {
    processor := NewTaskProcessor("SizeTestProcessor", 10000, 0)

    tests := []struct {
        name     string
        dataSize int
        taskType string
    }{
        {"small data - 10 bytes", 10, "text"},
        {"medium data - 100 bytes", 100, "text"},
        {"large data - 1000 bytes", 1000, "text"},
        {"small data reverse", 10, "data"},
        {"medium data reverse", 100, "data"},
        {"large data reverse", 1000, "data"},
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            data := make([]byte, tt.dataSize)
            for i := range data {
                data[i] = byte('a' + (i % 26))
            }

            task := &AutomationTask{
                ID:        "size-test",
                Type:      tt.taskType,
                Data:      data,
                Status:    StatusPending,
                CreatedAt: time.Now(),
            }

            start := time.Now()
            err := processor.Process(task)
            duration := time.Since(start)

            if err != nil {
                t.Fatalf("Expected no error, got %v", err)
            }

            if task.Status != StatusCompleted {
                t.Errorf("Expected status %v, got %v", StatusCompleted, task.Status)
            }

            // Log processing time for analysis
            t.Logf("Processed %d bytes in %v", tt.dataSize, duration)
        })
    }
}
```

**Hands-on Exercise 2: Sub-Tests and Advanced Testing Patterns**:

Implementing sub-tests and advanced testing organizational patterns:

**Extended Main Code (queue.go):**

```go
package automation

import (
    "fmt"
    "sort"
    "sync"
    "time"
)

// TaskQueue manages a queue of tasks with advanced features
type TaskQueue struct {
    tasks    []*AutomationTask
    capacity int
    mu       sync.RWMutex
    stats    QueueStats
}

// QueueStats tracks queue performance metrics
type QueueStats struct {
    TotalAdded     int64
    TotalProcessed int64
    TotalDropped   int64
    AverageWaitTime time.Duration
    mu             sync.RWMutex
}

func NewTaskQueue(capacity int) *TaskQueue {
    return &TaskQueue{
        tasks:    make([]*AutomationTask, 0, capacity),
        capacity: capacity,
    }
}

// Add adds a task to the queue with thread safety
func (tq *TaskQueue) Add(task *AutomationTask) error {
    tq.mu.Lock()
    defer tq.mu.Unlock()

    if len(tq.tasks) >= tq.capacity {
        tq.stats.mu.Lock()
        tq.stats.TotalDropped++
        tq.stats.mu.Unlock()
        return fmt.Errorf("queue is full (capacity: %d)", tq.capacity)
    }

    tq.tasks = append(tq.tasks, task)

    tq.stats.mu.Lock()
    tq.stats.TotalAdded++
    tq.stats.mu.Unlock()

    return nil
}

// Next retrieves the highest priority pending task
func (tq *TaskQueue) Next() *AutomationTask {
    tq.mu.Lock()
    defer tq.mu.Unlock()

    if len(tq.tasks) == 0 {
        return nil
    }

    // Find highest priority pending task
    var bestIndex = -1
    var bestPriority = -1

    for i, task := range tq.tasks {
        if task.Status == StatusPending && task.Priority > bestPriority {
            bestIndex = i
            bestPriority = task.Priority
        }
    }

    if bestIndex == -1 {
        return nil
    }

    task := tq.tasks[bestIndex]
    // Remove from queue
    tq.tasks = append(tq.tasks[:bestIndex], tq.tasks[bestIndex+1:]...)

    tq.stats.mu.Lock()
    tq.stats.TotalProcessed++
    tq.stats.mu.Unlock()

    return task
}

// Size returns the current queue size (thread-safe)
func (tq *TaskQueue) Size() int {
    tq.mu.RLock()
    defer tq.mu.RUnlock()
    return len(tq.tasks)
}

// PendingCount returns the number of pending tasks
func (tq *TaskQueue) PendingCount() int {
    tq.mu.RLock()
    defer tq.mu.RUnlock()

    count := 0
    for _, task := range tq.tasks {
        if task.Status == StatusPending {
            count++
        }
    }
    return count
}

// GetTasksByPriority returns tasks sorted by priority (highest first)
func (tq *TaskQueue) GetTasksByPriority() []*AutomationTask {
    tq.mu.RLock()
    defer tq.mu.RUnlock()

    // Create a copy to avoid race conditions
    tasks := make([]*AutomationTask, len(tq.tasks))
    copy(tasks, tq.tasks)

    sort.Slice(tasks, func(i, j int) bool {
        return tasks[i].Priority > tasks[j].Priority
    })

    return tasks
}

// GetTasksByType returns tasks filtered by type
func (tq *TaskQueue) GetTasksByType(taskType string) []*AutomationTask {
    tq.mu.RLock()
    defer tq.mu.RUnlock()

    var filtered []*AutomationTask
    for _, task := range tq.tasks {
        if task.Type == taskType {
            filtered = append(filtered, task)
        }
    }

    return filtered
}

// Clear removes all tasks from the queue
func (tq *TaskQueue) Clear() {
    tq.mu.Lock()
    defer tq.mu.Unlock()

    tq.tasks = tq.tasks[:0]
}

// GetStats returns current queue statistics
func (tq *TaskQueue) GetStats() QueueStats {
    tq.stats.mu.RLock()
    defer tq.stats.mu.RUnlock()

    return QueueStats{
        TotalAdded:     tq.stats.TotalAdded,
        TotalProcessed: tq.stats.TotalProcessed,
        TotalDropped:   tq.stats.TotalDropped,
        AverageWaitTime: tq.stats.AverageWaitTime,
    }
}

// BatchProcessor handles batch processing of tasks
type BatchProcessor struct {
    processor *TaskProcessor
    batchSize int
    timeout   time.Duration
}

func NewBatchProcessor(processor *TaskProcessor, batchSize int, timeout time.Duration) *BatchProcessor {
    return &BatchProcessor{
        processor: processor,
        batchSize: batchSize,
        timeout:   timeout,
    }
}

// ProcessBatch processes a batch of tasks
func (bp *BatchProcessor) ProcessBatch(tasks []*AutomationTask) ([]ProcessResult, error) {
    if len(tasks) == 0 {
        return nil, fmt.Errorf("no tasks to process")
    }

    if len(tasks) > bp.batchSize {
        return nil, fmt.Errorf("batch size %d exceeds maximum %d", len(tasks), bp.batchSize)
    }

    results := make([]ProcessResult, len(tasks))

    for i, task := range tasks {
        start := time.Now()
        err := bp.processor.Process(task)
        duration := time.Since(start)

        results[i] = ProcessResult{
            TaskID:   task.ID,
            Success:  err == nil,
            Error:    err,
            Duration: duration,
        }
    }

    return results, nil
}

// ProcessResult represents the result of processing a single task
type ProcessResult struct {
    TaskID   string
    Success  bool
    Error    error
    Duration time.Duration
}
```

**Advanced Test File (queue_test.go):**

```go
package automation

import (
    "fmt"
    "sync"
    "testing"
    "time"
)

// PART 1: Sub-Tests for TaskQueue
// Sub-tests organize related test cases under a common parent test

func TestTaskQueue(t *testing.T) {
    // Sub-test for basic queue operations
    t.Run("BasicOperations", func(t *testing.T) {
        t.Run("Add and Size", func(t *testing.T) {
            queue := NewTaskQueue(5)

            task := &AutomationTask{
                ID:     "test-1",
                Type:   "text",
                Status: StatusPending,
            }

            err := queue.Add(task)
            if err != nil {
                t.Fatalf("Expected no error, got %v", err)
            }

            if queue.Size() != 1 {
                t.Errorf("Expected size 1, got %d", queue.Size())
            }

            if queue.PendingCount() != 1 {
                t.Errorf("Expected pending count 1, got %d", queue.PendingCount())
            }
        })

        t.Run("Empty Queue Next", func(t *testing.T) {
            queue := NewTaskQueue(5)

            task := queue.Next()
            if task != nil {
                t.Error("Expected nil from empty queue, got task")
            }
        })

        t.Run("Clear Queue", func(t *testing.T) {
            queue := NewTaskQueue(5)

            // Add some tasks
            for i := 0; i < 3; i++ {
                task := &AutomationTask{
                    ID:     fmt.Sprintf("test-%d", i),
                    Status: StatusPending,
                }
                queue.Add(task)
            }

            if queue.Size() != 3 {
                t.Errorf("Expected size 3, got %d", queue.Size())
            }

            queue.Clear()

            if queue.Size() != 0 {
                t.Errorf("Expected size 0 after clear, got %d", queue.Size())
            }
        })
    })

    // Sub-test for capacity management
    t.Run("CapacityManagement", func(t *testing.T) {
        t.Run("Capacity Limit", func(t *testing.T) {
            queue := NewTaskQueue(2)

            // Add tasks up to capacity
            for i := 0; i < 2; i++ {
                task := &AutomationTask{
                    ID:     fmt.Sprintf("test-%d", i),
                    Status: StatusPending,
                }
                if err := queue.Add(task); err != nil {
                    t.Fatalf("Expected no error for task %d, got %v", i, err)
                }
            }

            // Try to add one more (should fail)
            task := &AutomationTask{
                ID:     "overflow",
                Status: StatusPending,
            }
            err := queue.Add(task)

            if err == nil {
                t.Error("Expected error when exceeding capacity, got nil")
            }

            // Check stats
            stats := queue.GetStats()
            if stats.TotalAdded != 2 {
                t.Errorf("Expected 2 tasks added, got %d", stats.TotalAdded)
            }

            if stats.TotalDropped != 1 {
                t.Errorf("Expected 1 task dropped, got %d", stats.TotalDropped)
            }
        })

        t.Run("Zero Capacity", func(t *testing.T) {
            queue := NewTaskQueue(0)

            task := &AutomationTask{
                ID:     "test",
                Status: StatusPending,
            }

            err := queue.Add(task)
            if err == nil {
                t.Error("Expected error for zero capacity queue, got nil")
            }
        })
    })

    // Sub-test for priority ordering
    t.Run("PriorityOrdering", func(t *testing.T) {
        t.Run("Priority Queue Behavior", func(t *testing.T) {
            queue := NewTaskQueue(10)

            // Add tasks with different priorities
            priorities := []int{3, 1, 5, 2, 4}
            for i, priority := range priorities {
                task := &AutomationTask{
                    ID:       fmt.Sprintf("task-%d", i),
                    Priority: priority,
                    Status:   StatusPending,
                }
                queue.Add(task)
            }

            // Should get tasks in priority order (highest first)
            expectedOrder := []int{5, 4, 3, 2, 1}
            for _, expectedPriority := range expectedOrder {
                task := queue.Next()
                if task == nil {
                    t.Fatal("Expected task, got nil")
                }

                if task.Priority != expectedPriority {
                    t.Errorf("Expected priority %d, got %d", expectedPriority, task.Priority)
                }
            }
        })

        t.Run("Same Priority FIFO", func(t *testing.T) {
            queue := NewTaskQueue(10)

            // Add tasks with same priority
            for i := 0; i < 3; i++ {
                task := &AutomationTask{
                    ID:       fmt.Sprintf("same-priority-%d", i),
                    Priority: 5,
                    Status:   StatusPending,
                }
                queue.Add(task)
            }

            // Should get first added task first (FIFO for same priority)
            task := queue.Next()
            if task == nil {
                t.Fatal("Expected task, got nil")
            }

            if task.ID != "same-priority-0" {
                t.Errorf("Expected first task, got %s", task.ID)
            }
        })

        t.Run("GetTasksByPriority", func(t *testing.T) {
            queue := NewTaskQueue(10)

            priorities := []int{1, 5, 3, 2, 4}
            for i, priority := range priorities {
                task := &AutomationTask{
                    ID:       fmt.Sprintf("priority-task-%d", i),
                    Priority: priority,
                    Status:   StatusPending,
                }
                queue.Add(task)
            }

            sortedTasks := queue.GetTasksByPriority()

            if len(sortedTasks) != 5 {
                t.Errorf("Expected 5 tasks, got %d", len(sortedTasks))
            }

            // Check if sorted by priority (highest first)
            expectedPriorities := []int{5, 4, 3, 2, 1}
            for i, expectedPriority := range expectedPriorities {
                if sortedTasks[i].Priority != expectedPriority {
                    t.Errorf("Expected priority %d at index %d, got %d",
                        expectedPriority, i, sortedTasks[i].Priority)
                }
            }
        })
    })

    // Sub-test for filtering and querying
    t.Run("FilteringAndQuerying", func(t *testing.T) {
        t.Run("GetTasksByType", func(t *testing.T) {
            queue := NewTaskQueue(10)

            taskTypes := []string{"text", "data", "text", "file", "text"}
            for i, taskType := range taskTypes {
                task := &AutomationTask{
                    ID:     fmt.Sprintf("type-task-%d", i),
                    Type:   taskType,
                    Status: StatusPending,
                }
                queue.Add(task)
            }

            textTasks := queue.GetTasksByType("text")
            if len(textTasks) != 3 {
                t.Errorf("Expected 3 text tasks, got %d", len(textTasks))
            }

            dataTasks := queue.GetTasksByType("data")
            if len(dataTasks) != 1 {
                t.Errorf("Expected 1 data task, got %d", len(dataTasks))
            }

            nonExistentTasks := queue.GetTasksByType("nonexistent")
            if len(nonExistentTasks) != 0 {
                t.Errorf("Expected 0 nonexistent tasks, got %d", len(nonExistentTasks))
            }
        })

        t.Run("Mixed Status Tasks", func(t *testing.T) {
            queue := NewTaskQueue(10)

            statuses := []TaskStatus{StatusPending, StatusCompleted, StatusPending, StatusFailed}
            for i, status := range statuses {
                task := &AutomationTask{
                    ID:     fmt.Sprintf("status-task-%d", i),
                    Status: status,
                    Priority: 5,
                }
                queue.Add(task)
            }

            if queue.Size() != 4 {
                t.Errorf("Expected total size 4, got %d", queue.Size())
            }

            if queue.PendingCount() != 2 {
                t.Errorf("Expected 2 pending tasks, got %d", queue.PendingCount())
            }

            // Next should only return pending tasks
            task := queue.Next()
            if task == nil {
                t.Fatal("Expected pending task, got nil")
            }

            if task.Status != StatusPending {
                t.Errorf("Expected pending status, got %v", task.Status)
            }
        })
    })

    // Sub-test for thread safety
    t.Run("ThreadSafety", func(t *testing.T) {
        t.Run("Concurrent Add and Next", func(t *testing.T) {
            queue := NewTaskQueue(100)

            var wg sync.WaitGroup
            const numGoroutines = 10
            const tasksPerGoroutine = 10

            // Concurrent adds
            for i := 0; i < numGoroutines; i++ {
                wg.Add(1)
                go func(goroutineID int) {
                    defer wg.Done()

                    for j := 0; j < tasksPerGoroutine; j++ {
                        task := &AutomationTask{
                            ID:       fmt.Sprintf("concurrent-%d-%d", goroutineID, j),
                            Priority: j,
                            Status:   StatusPending,
                        }
                        queue.Add(task)
                    }
                }(i)
            }

            wg.Wait()

            expectedSize := numGoroutines * tasksPerGoroutine
            if queue.Size() != expectedSize {
                t.Errorf("Expected size %d, got %d", expectedSize, queue.Size())
            }

            // Concurrent processing
            processedCount := 0
            for i := 0; i < numGoroutines; i++ {
                wg.Add(1)
                go func() {
                    defer wg.Done()

                    for j := 0; j < tasksPerGoroutine; j++ {
                        task := queue.Next()
                        if task != nil {
                            processedCount++
                        }
                    }
                }()
            }

            wg.Wait()

            if queue.Size() != 0 {
                t.Errorf("Expected empty queue after processing, got size %d", queue.Size())
            }
        })
    })
}

// PART 2: Sub-Tests for BatchProcessor
func TestBatchProcessor(t *testing.T) {
    processor := NewTaskProcessor("BatchTestProcessor", 1000, 0)

    t.Run("BatchProcessing", func(t *testing.T) {
        t.Run("Valid Batch", func(t *testing.T) {
            batchProcessor := NewBatchProcessor(processor, 5, 1*time.Second)

            tasks := []*AutomationTask{
                {ID: "batch-1", Type: "text", Data: []byte("hello"), Status: StatusPending},
                {ID: "batch-2", Type: "data", Data: []byte("world"), Status: StatusPending},
                {ID: "batch-3", Type: "file", Data: []byte("test"), Status: StatusPending},
            }

            results, err := batchProcessor.ProcessBatch(tasks)
            if err != nil {
                t.Fatalf("Expected no error, got %v", err)
            }

            if len(results) != 3 {
                t.Errorf("Expected 3 results, got %d", len(results))
            }

            for i, result := range results {
                if !result.Success {
                    t.Errorf("Expected success for task %d, got error: %v", i, result.Error)
                }

                if result.Duration == 0 {
                    t.Errorf("Expected non-zero duration for task %d", i)
                }
            }
        })

        t.Run("Empty Batch", func(t *testing.T) {
            batchProcessor := NewBatchProcessor(processor, 5, 1*time.Second)

            results, err := batchProcessor.ProcessBatch([]*AutomationTask{})
            if err == nil {
                t.Error("Expected error for empty batch, got nil")
            }

            if results != nil {
                t.Error("Expected nil results for empty batch")
            }
        })

        t.Run("Oversized Batch", func(t *testing.T) {
            batchProcessor := NewBatchProcessor(processor, 2, 1*time.Second)

            tasks := []*AutomationTask{
                {ID: "over-1", Type: "text", Data: []byte("test1"), Status: StatusPending},
                {ID: "over-2", Type: "text", Data: []byte("test2"), Status: StatusPending},
                {ID: "over-3", Type: "text", Data: []byte("test3"), Status: StatusPending},
            }

            results, err := batchProcessor.ProcessBatch(tasks)
            if err == nil {
                t.Error("Expected error for oversized batch, got nil")
            }

            if results != nil {
                t.Error("Expected nil results for oversized batch")
            }
        })

        t.Run("Mixed Success and Failure", func(t *testing.T) {
            batchProcessor := NewBatchProcessor(processor, 5, 1*time.Second)

            tasks := []*AutomationTask{
                {ID: "mixed-1", Type: "text", Data: []byte("valid"), Status: StatusPending},
                {ID: "mixed-2", Type: "invalid", Data: []byte("invalid"), Status: StatusPending},
                {ID: "mixed-3", Type: "data", Data: []byte("valid"), Status: StatusPending},
            }

            results, err := batchProcessor.ProcessBatch(tasks)
            if err != nil {
                t.Fatalf("Expected no error from batch processing, got %v", err)
            }

            if len(results) != 3 {
                t.Errorf("Expected 3 results, got %d", len(results))
            }

            // Check individual results
            if !results[0].Success {
                t.Error("Expected first task to succeed")
            }

            if results[1].Success {
                t.Error("Expected second task to fail")
            }

            if !results[2].Success {
                t.Error("Expected third task to succeed")
            }
        })
    })
}

// PART 3: Parallel Sub-Tests
func TestTaskQueue_Parallel(t *testing.T) {
    t.Run("ParallelOperations", func(t *testing.T) {
        t.Parallel() // This test can run in parallel with other parallel tests

        queue := NewTaskQueue(1000)

        t.Run("ParallelAdds", func(t *testing.T) {
            t.Parallel()

            var wg sync.WaitGroup
            const numWorkers = 50
            const tasksPerWorker = 20

            for i := 0; i < numWorkers; i++ {
                wg.Add(1)
                go func(workerID int) {
                    defer wg.Done()

                    for j := 0; j < tasksPerWorker; j++ {
                        task := &AutomationTask{
                            ID:     fmt.Sprintf("parallel-%d-%d", workerID, j),
                            Status: StatusPending,
                        }
                        queue.Add(task)
                    }
                }(i)
            }

            wg.Wait()

            expectedSize := numWorkers * tasksPerWorker
            if queue.Size() != expectedSize {
                t.Errorf("Expected size %d, got %d", expectedSize, queue.Size())
            }
        })

        t.Run("ParallelReads", func(t *testing.T) {
            t.Parallel()

            // Add some tasks first
            for i := 0; i < 100; i++ {
                task := &AutomationTask{
                    ID:     fmt.Sprintf("read-test-%d", i),
                    Type:   "text",
                    Status: StatusPending,
                }
                queue.Add(task)
            }

            var wg sync.WaitGroup
            const numReaders = 10

            for i := 0; i < numReaders; i++ {
                wg.Add(1)
                go func(readerID int) {
                    defer wg.Done()

                    // Perform various read operations
                    for j := 0; j < 10; j++ {
                        _ = queue.Size()
                        _ = queue.PendingCount()
                        _ = queue.GetTasksByType("text")
                        _ = queue.GetTasksByPriority()
                    }
                }(i)
            }

            wg.Wait()
        })
    })
}
```

**Hands-on Exercise 3: Benchmarking and Performance Profiling**:

Implementing comprehensive benchmarks and performance profiling techniques:

**Performance-Critical Functions (performance.go):**

```go
package automation

import (
    "fmt"
    "math/rand"
    "time"
)

// Performance-critical function for benchmarking
func ProcessLargeDataset(data [][]byte, processor *TaskProcessor) ([]*AutomationTask, error) {
    tasks := make([]*AutomationTask, len(data))

    for i, item := range data {
        task := &AutomationTask{
            ID:        fmt.Sprintf("dataset-task-%d", i),
            Type:      "data",
            Data:      item,
            Priority:  i % 10,
            CreatedAt: time.Now(),
            Status:    StatusPending,
        }

        if err := processor.Process(task); err != nil {
            return nil, fmt.Errorf("failed to process task %d: %w", i, err)
        }

        tasks[i] = task
    }

    return tasks, nil
}

// Memory-intensive function for memory profiling
func CreateLargeTaskBatch(count int, dataSize int) []*AutomationTask {
    tasks := make([]*AutomationTask, count)

    for i := 0; i < count; i++ {
        data := make([]byte, dataSize)
        for j := range data {
            data[j] = byte(j % 256)
        }

        tasks[i] = &AutomationTask{
            ID:        fmt.Sprintf("large-task-%d", i),
            Type:      "data",
            Data:      data,
            Priority:  i % 10,
            CreatedAt: time.Now(),
            Status:    StatusPending,
        }
    }

    return tasks
}

// CPU-intensive function for CPU profiling
func ProcessComplexData(data []byte, iterations int) []byte {
    result := make([]byte, len(data))
    copy(result, data)

    for iter := 0; iter < iterations; iter++ {
        // Simulate complex processing
        for i := 0; i < len(result); i++ {
            // Apply multiple transformations
            result[i] = byte((int(result[i]) * 7 + 13) % 256)
            result[i] = result[i] ^ byte(iter%256)

            // Simulate hash computation
            hash := uint32(result[i])
            hash = hash*31 + uint32(i)
            hash = hash*31 + uint32(iter)
            result[i] = byte(hash % 256)
        }

        // Simulate sorting operation
        if iter%10 == 0 {
            bubbleSort(result)
        }
    }

    return result
}

// bubbleSort for CPU-intensive operations
func bubbleSort(data []byte) {
    n := len(data)
    for i := 0; i < n-1; i++ {
        for j := 0; j < n-i-1; j++ {
            if data[j] > data[j+1] {
                data[j], data[j+1] = data[j+1], data[j]
            }
        }
    }
}

// Memory allocation patterns for profiling
func AllocateMemoryPatterns(pattern string, size int) [][]byte {
    switch pattern {
    case "sequential":
        return allocateSequential(size)
    case "random":
        return allocateRandom(size)
    case "fragmented":
        return allocateFragmented(size)
    default:
        return allocateSequential(size)
    }
}

func allocateSequential(size int) [][]byte {
    slices := make([][]byte, size)
    for i := 0; i < size; i++ {
        slices[i] = make([]byte, i+1)
    }
    return slices
}

func allocateRandom(size int) [][]byte {
    slices := make([][]byte, size)
    for i := 0; i < size; i++ {
        randomSize := rand.Intn(1000) + 1
        slices[i] = make([]byte, randomSize)
    }
    return slices
}

func allocateFragmented(size int) [][]byte {
    slices := make([][]byte, size)
    for i := 0; i < size; i++ {
        if i%2 == 0 {
            slices[i] = make([]byte, 1024)
        } else {
            slices[i] = make([]byte, 16)
        }
    }
    return slices
}

// Concurrent processing for race detection and performance testing
func ProcessConcurrently(tasks []*AutomationTask, processor *TaskProcessor, workers int) error {
    taskChan := make(chan *AutomationTask, len(tasks))
    errorChan := make(chan error, len(tasks))

    // Send tasks to channel
    for _, task := range tasks {
        taskChan <- task
    }
    close(taskChan)

    // Start workers
    for i := 0; i < workers; i++ {
        go func(workerID int) {
            for task := range taskChan {
                if err := processor.Process(task); err != nil {
                    errorChan <- fmt.Errorf("worker %d failed to process task %s: %w",
                        workerID, task.ID, err)
                    return
                }
            }
            errorChan <- nil
        }(i)
    }

    // Collect results
    for i := 0; i < workers; i++ {
        if err := <-errorChan; err != nil {
            return err
        }
    }

    return nil
}
```

**Comprehensive Benchmark File (performance_test.go):**

```go
package automation

import (
    "bytes"
    "fmt"
    "runtime"
    "testing"
    "time"
)

// PART 1: Basic Benchmarks
// Basic benchmarks measure performance of individual operations

func BenchmarkTaskProcessor_ProcessText(b *testing.B) {
    processor := NewTaskProcessor("BenchProcessor", 1000, 0)

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        task := &AutomationTask{
            ID:     "bench-task",
            Type:   "text",
            Data:   []byte("benchmark data for text processing"),
            Status: StatusPending,
        }
        processor.Process(task)
    }
}

func BenchmarkTaskProcessor_ProcessData(b *testing.B) {
    processor := NewTaskProcessor("BenchProcessor", 1000, 0)

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        task := &AutomationTask{
            ID:     "bench-task",
            Type:   "data",
            Data:   bytes.Repeat([]byte("a"), 100),
            Status: StatusPending,
        }
        processor.Process(task)
    }
}

func BenchmarkTaskProcessor_ProcessFile(b *testing.B) {
    processor := NewTaskProcessor("BenchProcessor", 1000, 0)

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        task := &AutomationTask{
            ID:     "bench-task",
            Type:   "file",
            Data:   []byte("file content for benchmarking"),
            Status: StatusPending,
        }
        processor.Process(task)
    }
}

func BenchmarkTaskProcessor_ProcessEmail(b *testing.B) {
    processor := NewTaskProcessor("BenchProcessor", 1000, 0)

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        task := &AutomationTask{
            ID:     "bench-task",
            Type:   "email",
            Data:   []byte("<EMAIL>"),
            Status: StatusPending,
        }
        processor.Process(task)
    }
}

func BenchmarkTaskProcessor_ProcessJSON(b *testing.B) {
    processor := NewTaskProcessor("BenchProcessor", 1000, 0)

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        task := &AutomationTask{
            ID:     "bench-task",
            Type:   "json",
            Data:   []byte("sample json data"),
            Status: StatusPending,
        }
        processor.Process(task)
    }
}

// PART 2: Sub-Benchmarks for Different Data Sizes
// Sub-benchmarks allow testing performance across different parameters

func BenchmarkProcessLargeDataset(b *testing.B) {
    processor := NewTaskProcessor("BenchProcessor", 10000, 0)

    sizes := []int{10, 50, 100, 500, 1000}

    for _, size := range sizes {
        b.Run(fmt.Sprintf("size-%d", size), func(b *testing.B) {
            data := make([][]byte, size)
            for i := range data {
                data[i] = bytes.Repeat([]byte("x"), 50)
            }

            b.ResetTimer()
            for i := 0; i < b.N; i++ {
                ProcessLargeDataset(data, processor)
            }
        })
    }
}

func BenchmarkCreateLargeTaskBatch(b *testing.B) {
    counts := []int{10, 100, 1000}
    dataSizes := []int{64, 256, 1024}

    for _, count := range counts {
        for _, dataSize := range dataSizes {
            name := fmt.Sprintf("count-%d-size-%d", count, dataSize)
            b.Run(name, func(b *testing.B) {
                b.ResetTimer()
                for i := 0; i < b.N; i++ {
                    CreateLargeTaskBatch(count, dataSize)
                }
            })
        }
    }
}

func BenchmarkProcessComplexData(b *testing.B) {
    dataSizes := []int{100, 500, 1000}
    iterations := []int{10, 50, 100}

    for _, dataSize := range dataSizes {
        for _, iter := range iterations {
            name := fmt.Sprintf("data-%d-iter-%d", dataSize, iter)
            b.Run(name, func(b *testing.B) {
                data := make([]byte, dataSize)
                for i := range data {
                    data[i] = byte(i % 256)
                }

                b.ResetTimer()
                for i := 0; i < b.N; i++ {
                    ProcessComplexData(data, iter)
                }
            })
        }
    }
}

// PART 3: Memory Allocation Benchmarks
// These benchmarks focus on memory allocation patterns and GC pressure

func BenchmarkMemoryAllocation(b *testing.B) {
    patterns := []string{"sequential", "random", "fragmented"}
    sizes := []int{100, 500, 1000}

    for _, pattern := range patterns {
        for _, size := range sizes {
            name := fmt.Sprintf("%s-size-%d", pattern, size)
            b.Run(name, func(b *testing.B) {
                b.ResetTimer()
                for i := 0; i < b.N; i++ {
                    AllocateMemoryPatterns(pattern, size)
                }
            })
        }
    }
}

func BenchmarkTaskQueue_Operations(b *testing.B) {
    b.Run("Add", func(b *testing.B) {
        queue := NewTaskQueue(b.N + 1000) // Ensure capacity

        b.ResetTimer()
        for i := 0; i < b.N; i++ {
            task := &AutomationTask{
                ID:     fmt.Sprintf("bench-task-%d", i),
                Status: StatusPending,
            }
            queue.Add(task)
        }
    })

    b.Run("Next", func(b *testing.B) {
        queue := NewTaskQueue(b.N + 1000)

        // Pre-populate queue
        for i := 0; i < b.N; i++ {
            task := &AutomationTask{
                ID:       fmt.Sprintf("bench-task-%d", i),
                Priority: i % 10,
                Status:   StatusPending,
            }
            queue.Add(task)
        }

        b.ResetTimer()
        for i := 0; i < b.N; i++ {
            queue.Next()
        }
    })

    b.Run("Size", func(b *testing.B) {
        queue := NewTaskQueue(1000)

        // Add some tasks
        for i := 0; i < 500; i++ {
            task := &AutomationTask{
                ID:     fmt.Sprintf("bench-task-%d", i),
                Status: StatusPending,
            }
            queue.Add(task)
        }

        b.ResetTimer()
        for i := 0; i < b.N; i++ {
            queue.Size()
        }
    })

    b.Run("GetTasksByPriority", func(b *testing.B) {
        queue := NewTaskQueue(1000)

        // Add tasks with different priorities
        for i := 0; i < 500; i++ {
            task := &AutomationTask{
                ID:       fmt.Sprintf("bench-task-%d", i),
                Priority: i % 10,
                Status:   StatusPending,
            }
            queue.Add(task)
        }

        b.ResetTimer()
        for i := 0; i < b.N; i++ {
            queue.GetTasksByPriority()
        }
    })
}

// PART 4: Concurrent Performance Benchmarks
func BenchmarkConcurrentProcessing(b *testing.B) {
    processor := NewTaskProcessor("ConcurrentBenchProcessor", 10000, 0)
    workerCounts := []int{1, 2, 4, 8, 16}

    for _, workers := range workerCounts {
        b.Run(fmt.Sprintf("workers-%d", workers), func(b *testing.B) {
            tasks := make([]*AutomationTask, 100)
            for i := range tasks {
                tasks[i] = &AutomationTask{
                    ID:     fmt.Sprintf("concurrent-task-%d", i),
                    Type:   "text",
                    Data:   []byte("concurrent processing test data"),
                    Status: StatusPending,
                }
            }

            b.ResetTimer()
            for i := 0; i < b.N; i++ {
                // Reset task status for each iteration
                for _, task := range tasks {
                    task.Status = StatusPending
                }

                ProcessConcurrently(tasks, processor, workers)
            }
        })
    }
}

// PART 5: Memory Benchmarks with Allocation Tracking
func BenchmarkMemoryIntensive(b *testing.B) {
    b.Run("TaskCreation", func(b *testing.B) {
        b.ReportAllocs() // Report memory allocations

        b.ResetTimer()
        for i := 0; i < b.N; i++ {
            task := &AutomationTask{
                ID:        fmt.Sprintf("memory-task-%d", i),
                Type:      "text",
                Data:      make([]byte, 1024),
                Priority:  i % 10,
                CreatedAt: time.Now(),
                Status:    StatusPending,
                Metadata:  make(map[string]string),
            }
            _ = task // Prevent optimization
        }
    })

    b.Run("BatchCreation", func(b *testing.B) {
        b.ReportAllocs()

        b.ResetTimer()
        for i := 0; i < b.N; i++ {
            CreateLargeTaskBatch(100, 256)
        }
    })

    b.Run("QueueOperations", func(b *testing.B) {
        b.ReportAllocs()

        b.ResetTimer()
        for i := 0; i < b.N; i++ {
            queue := NewTaskQueue(100)

            for j := 0; j < 50; j++ {
                task := &AutomationTask{
                    ID:     fmt.Sprintf("queue-task-%d", j),
                    Status: StatusPending,
                }
                queue.Add(task)
            }

            for j := 0; j < 25; j++ {
                queue.Next()
            }
        }
    })
}

// PART 6: CPU Profiling Benchmarks
func BenchmarkCPUIntensive(b *testing.B) {
    b.Run("ComplexProcessing", func(b *testing.B) {
        data := make([]byte, 1000)
        for i := range data {
            data[i] = byte(i % 256)
        }

        b.ResetTimer()
        for i := 0; i < b.N; i++ {
            ProcessComplexData(data, 50)
        }
    })

    b.Run("SortingOperations", func(b *testing.B) {
        b.ResetTimer()
        for i := 0; i < b.N; i++ {
            data := make([]byte, 1000)
            for j := range data {
                data[j] = byte((1000 - j) % 256)
            }
            bubbleSort(data)
        }
    })
}

// PART 7: Comparative Benchmarks
func BenchmarkProcessingComparison(b *testing.B) {
    processor := NewTaskProcessor("ComparisonProcessor", 10000, 0)

    taskTypes := []string{"text", "data", "file", "email", "json"}

    for _, taskType := range taskTypes {
        b.Run(fmt.Sprintf("type-%s", taskType), func(b *testing.B) {
            var testData []byte
            switch taskType {
            case "text":
                testData = []byte("sample text for processing")
            case "data":
                testData = bytes.Repeat([]byte("d"), 100)
            case "file":
                testData = []byte("file content data")
            case "email":
                testData = []byte("<EMAIL>")
            case "json":
                testData = []byte("json data content")
            }

            b.ResetTimer()
            for i := 0; i < b.N; i++ {
                task := &AutomationTask{
                    ID:     "comparison-task",
                    Type:   taskType,
                    Data:   testData,
                    Status: StatusPending,
                }
                processor.Process(task)
            }
        })
    }
}

// PART 8: Example Benchmark with Custom Metrics
func BenchmarkWithCustomMetrics(b *testing.B) {
    processor := NewTaskProcessor("MetricsProcessor", 10000, 0)

    var totalBytes int64
    var totalTasks int64

    b.ResetTimer()
    start := time.Now()

    for i := 0; i < b.N; i++ {
        data := make([]byte, 256)
        task := &AutomationTask{
            ID:     fmt.Sprintf("metrics-task-%d", i),
            Type:   "data",
            Data:   data,
            Status: StatusPending,
        }

        processor.Process(task)

        totalBytes += int64(len(data))
        totalTasks++
    }

    duration := time.Since(start)

    // Report custom metrics
    b.ReportMetric(float64(totalBytes)/duration.Seconds(), "bytes/sec")
    b.ReportMetric(float64(totalTasks)/duration.Seconds(), "tasks/sec")
    b.ReportMetric(float64(totalBytes)/float64(totalTasks), "avg_bytes/task")
}

// Benchmark example function for documentation
func ExampleBenchmarkUsage() {
    // Run benchmarks with:
    // go test -bench=.
    // go test -bench=BenchmarkTaskProcessor
    // go test -bench=. -benchmem
    // go test -bench=. -cpuprofile=cpu.prof
    // go test -bench=. -memprofile=mem.prof
    // go test -bench=. -count=5
    // go test -bench=. -benchtime=10s
}

// Helper function to demonstrate profiling setup
func init() {
    // Set GOMAXPROCS for consistent benchmarking
    runtime.GOMAXPROCS(runtime.NumCPU())
}
```

**Profiling Guide (PROFILING.md):**

```markdown
# Performance Profiling Guide

## Running Benchmarks

### Basic Benchmarks
```bash
# Run all benchmarks
go test -bench=.

# Run specific benchmark
go test -bench=BenchmarkTaskProcessor

# Include memory allocation stats
go test -bench=. -benchmem

# Run benchmarks multiple times for accuracy
go test -bench=. -count=5

# Run benchmarks for specific duration
go test -bench=. -benchtime=10s
```

### CPU Profiling

```bash
# Generate CPU profile
go test -bench=. -cpuprofile=cpu.prof

# Analyze CPU profile
go tool pprof cpu.prof

# Web interface for CPU profile
go tool pprof -http=:8080 cpu.prof
```

### Memory Profiling

```bash
# Generate memory profile
go test -bench=. -memprofile=mem.prof

# Analyze memory profile
go tool pprof mem.prof

# Web interface for memory profile
go tool pprof -http=:8080 mem.prof
```

### Execution Tracing

```bash
# Generate execution trace
go test -bench=BenchmarkConcurrentProcessing -trace=trace.out

# Analyze execution trace
go tool trace trace.out
```

## Profiling Commands

### In pprof Interactive Mode

- `top`: Show top functions by CPU usage
- `list <function>`: Show source code with annotations
- `web`: Generate SVG call graph
- `peek <function>`: Show callers and callees
- `disasm <function>`: Show assembly code

### Common Analysis Patterns

1. **CPU Hotspots**: Look for functions consuming most CPU time
2. **Memory Leaks**: Check for growing heap allocations
3. **Goroutine Leaks**: Monitor goroutine count over time
4. **Lock Contention**: Identify mutex bottlenecks

## Performance Optimization Tips

1. Use benchmarks to measure before optimizing
2. Profile in production-like conditions
3. Focus on the biggest bottlenecks first
4. Validate optimizations with benchmarks
5. Consider memory allocation patterns
6. Monitor garbage collection impact

**Running the Exercises:**:

```bash
# Exercise 1: Unit Testing and Table-Driven Tests
go test -v -run TestTaskProcessor
go test -v -run TestTaskProcessor_ErrorConditions
go test -v -run TestTaskProcessor_DataSizes

# Exercise 2: Sub-Tests and Advanced Testing Patterns
go test -v -run TestTaskQueue
go test -v -run TestBatchProcessor
go test -v -run TestTaskQueue_Parallel

# Exercise 3: Benchmarking and Performance Profiling
go test -bench=BenchmarkTaskProcessor -benchmem
go test -bench=BenchmarkProcessLargeDataset -benchmem
go test -bench=BenchmarkMemoryIntensive -benchmem

# CPU Profiling
go test -bench=BenchmarkCPUIntensive -cpuprofile=cpu.prof
go tool pprof cpu.prof

# Memory Profiling
go test -bench=BenchmarkMemoryAllocation -memprofile=mem.prof
go tool pprof mem.prof

# Execution Tracing
go test -bench=BenchmarkConcurrentProcessing -trace=trace.out
go tool trace trace.out

# Race Detection
go test -race -v

# Coverage Analysis
go test -cover -coverprofile=coverage.out
go tool cover -html=coverage.out
```

**Prerequisites**: Session 31

---

## Training Plan Summary and Next Steps

### ðŸŽ¯ **Training Completion Achievements**

Upon completing this comprehensive 32-session Go training plan, participants will have achieved:

**Core Language Mastery**:

- Deep understanding of Go's type system and memory model
- Expertise in pointers, escape analysis, and memory management
- Proficiency with Go's unique approach to constants and type safety
- Mastery of data structures (arrays, slices, maps, strings)

**Advanced Programming Concepts**:

- Methods and interfaces for clean code architecture
- Composition patterns and decoupling strategies
- Comprehensive error handling techniques
- Package design and code organization

**Concurrency and Performance**:

- Goroutines and the Go scheduler
- Channel patterns and concurrency safety
- Data race detection and prevention
- Performance optimization techniques

**Professional Development Practices**:

- Comprehensive testing strategies (unit, integration, benchmarking)
- Profiling and performance analysis
- Code quality and maintainability practices
- Automation-specific design patterns

### ðŸ“ˆ **Skill Progression Path**

**Beginner â†’ Intermediate (Sessions 1-16)**:

- Foundation concepts and basic programming
- Memory management and type safety
- Data structures and basic algorithms

**Intermediate â†’ Advanced (Sessions 17-24)**:

- Object-oriented concepts in Go
- Interface design and composition
- Error handling mastery

**Advanced â†’ Expert (Sessions 25-32)**:

- Concurrency and parallel programming
- Performance optimization
- Professional testing and profiling

### ðŸ›  **Practical Application Areas**

Graduates of this training will be equipped to build:

**Automation Systems**:

- Configuration management tools
- Task scheduling and execution engines
- Data processing pipelines
- System monitoring and alerting

**Integration Solutions**:

- API clients and servers
- Message queue processors
- Database integration layers
- File processing systems

**Performance-Critical Applications**:

- High-throughput data processors
- Real-time monitoring systems
- Efficient batch processing tools
- Memory-optimized applications

### ðŸ”„ **Continuous Learning Recommendations**

**Immediate Next Steps**:

1. Build a complete automation project using learned concepts
2. Contribute to open-source Go projects
3. Implement performance-critical components in production
4. Practice advanced concurrency patterns

**Advanced Topics for Further Study**:

- Go modules and dependency management
- Advanced reflection and code generation
- CGO and system programming
- Distributed systems patterns
- Microservices architecture with Go

**Community Engagement**:

- Join Go community forums and discussions
- Attend Go conferences and meetups
- Follow Go team blogs and release notes
- Participate in code reviews and mentoring

### ðŸ“š **Additional Resources**

**Official Documentation**:

- [Go Language Specification](https://golang.org/ref/spec)
- [Effective Go](https://golang.org/doc/effective_go.html)
- [Go Blog](https://blog.golang.org/)

**Advanced Learning**:

- "The Go Programming Language" by Donovan & Kernighan
- "Go in Action" by Kennedy, Ketelsen & St. Martin
- "Concurrency in Go" by Katherine Cox-Buday

**Tools and Utilities**:

- Go toolchain (`go build`, `go test`, `go mod`)
- Profiling tools (`go tool pprof`, `go tool trace`)
- Static analysis tools (`go vet`, `golint`, `staticcheck`)

### ðŸŽ‰ **Certification and Recognition**

**Internal Certification Path**:

- Complete all 32 sessions with hands-on exercises
- Build and present a capstone automation project
- Demonstrate proficiency in code reviews
- Mentor new team members in Go development

**Professional Development**:

- Contribute to team's Go coding standards
- Lead Go adoption initiatives
- Share knowledge through internal presentations
- Participate in Go community contributions

---

## Conclusion

This comprehensive Go training plan represents a complete journey from beginner to expert-level Go programming, specifically tailored for automation and integration teams. The curriculum is based on the industry-leading "Ultimate Go Programming" course content, enhanced with practical automation examples and modern Go best practices.

The training emphasizes:

- **Practical Application**: Every concept is demonstrated with real-world automation scenarios
- **Performance Awareness**: Deep understanding of memory management and optimization
- **Professional Practices**: Industry-standard testing, profiling, and code quality techniques
- **Progressive Learning**: Each session builds upon previous knowledge systematically

By completing this training, team members will be equipped with the knowledge and skills necessary to build high-performance, maintainable automation systems using Go, contributing effectively to the team's technical objectives and professional growth.

**Training Plan Version**: 2.0
**Last Updated**: July 2025
**Based on**: Ultimate Go Programming 2nd Edition + Go 1.24/1.25 Features
**Target Audience**: Automation and Integration Teams
